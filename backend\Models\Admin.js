const mongoose = require("mongoose");

const statusSchema = new mongoose.Schema({
  name: { type: String, required: true },
  color: { type: String, required: true },
  createdAt: { type: Date, default: Date.now }
});

const adminSchema = new mongoose.Schema({
  firstName: {
    type: String,
    required: true,
    trim: true
  },
  lastName: {
    type: String,
    required: true,
    trim: true
  },
  email: { 
    type: String, 
    unique: true,
    required: true,
    lowercase: true,
    trim: true
  },
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  password: {
    type: String,
    required: true
  },
  department: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Department",
    required: true
  },
  // Admin-specific fields
  permissions: {
    type: [String],
    default: ['manage_users', 'view_reports', 'manage_tasks']
  },
  lastLogin: {
    type: Date,
    default: null
  },
  // Account status
  isActive: {
    type: Boolean,
    default: true
  },
  isEmailVerified: {
    type: Boolean,
    default: true // Admins are verified by default when created by superadmin
  },
  // Created by superadmin
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User", // Reference to superadmin user
    required: true
  },
  customStatuses: [statusSchema],
}, {
  timestamps: true // This will automatically add createdAt and updatedAt fields
});

// Indexes for better query performance
adminSchema.index({ email: 1 });
adminSchema.index({ username: 1 });
adminSchema.index({ department: 1 });
adminSchema.index({ isActive: 1 });

// Virtual for full name
adminSchema.virtual('fullName').get(function() {
  return `${this.firstName} ${this.lastName}`;
});

// Ensure virtual fields are serialized
adminSchema.set('toJSON', { virtuals: true });

module.exports = mongoose.model("Admin", adminSchema);
