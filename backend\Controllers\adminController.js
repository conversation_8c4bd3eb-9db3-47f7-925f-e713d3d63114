const User = require("../Models/User");
const Admin = require("../Models/Admin");
const Task = require("../Models/Task");
const CompletedTask = require("../Models/CompletedTask");
const Notification = require("../Models/Notification");

// Get all users (admin only) - only regular users, not admins
exports.getAllUsers = async (req, res) => {
  try {
    // Only get regular users, not admins or superadmins
    const users = await User.find({ role: 'user' })
      .populate('department', 'name description')
      .select('-password')
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      count: users.length,
      users
    });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

// Get user details with tasks (admin only)
exports.getUserDetails = async (req, res) => {
  try {
    const { userId } = req.params;
    
    const user = await User.findById(userId).select('-password');
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // Get user's tasks
    const tasks = await Task.find({ userId }).sort({ date: -1 });
    const completedTasks = await CompletedTask.find({ userId }).sort({ completedAt: -1 });
    const notifications = await Notification.find({ userId }).sort({ createdAt: -1 });

    res.json({
      success: true,
      user,
      stats: {
        totalTasks: tasks.length,
        completedTasks: completedTasks.length,
        notifications: notifications.length
      },
      tasks,
      completedTasks: completedTasks.slice(0, 10), // Latest 10
      notifications: notifications.slice(0, 10) // Latest 10
    });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

// Get system statistics (admin only)
exports.getSystemStats = async (req, res) => {
  try {
    const totalUsers = await User.countDocuments({ role: 'user' });
    const totalTasks = await Task.countDocuments();
    const totalCompletedTasks = await CompletedTask.countDocuments();
    const totalNotifications = await Notification.countDocuments();

    // Get recent activity
    const recentUsers = await User.find({ role: 'user' })
      .populate('department', 'name')
      .select('-password')
      .sort({ createdAt: -1 })
      .limit(5);

    const recentTasks = await Task.find()
      .populate('userId', 'firstName lastName email')
      .sort({ createdAt: -1 })
      .limit(10);

    res.json({
      success: true,
      stats: {
        totalUsers,
        totalTasks,
        totalCompletedTasks,
        totalNotifications
      },
      recentActivity: {
        recentUsers,
        recentTasks
      }
    });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

// Delete user account (admin only)
exports.deleteUser = async (req, res) => {
  try {
    const { userId } = req.params;
    
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    if (user.role === 'administrator') {
      return res.status(400).json({ message: "Cannot delete administrator account" });
    }

    // Delete user's tasks and related data
    await Task.deleteMany({ userId });
    await CompletedTask.deleteMany({ userId });
    await Notification.deleteMany({ userId });
    
    // Delete user
    await User.findByIdAndDelete(userId);

    res.json({
      success: true,
      message: "User and all associated data deleted successfully"
    });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

// Get all completed tasks from all users (admin only)
exports.getAllCompletedTasks = async (req, res) => {
  try {
    const { filter, limit = 50, page = 1, userId, taskType } = req.query;
    let dateQuery = {};
    let userQuery = {};
    let typeQuery = {};

    // If specific user is requested
    if (userId) {
      userQuery.userId = userId;
    }

    // If specific task type is requested
    if (taskType) {
      typeQuery.taskType = taskType;
    }

    const now = new Date();

    // Apply date filter
    if (filter) {
      switch (filter) {
        case 'today':
          const dayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);
          const dayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999);
          dateQuery.completedAt = { $gte: dayStart, $lte: dayEnd };
          break;
        case 'week':
          const weekStart = new Date(now);
          weekStart.setDate(now.getDate() - now.getDay());
          weekStart.setHours(0, 0, 0, 0);
          const weekEnd = new Date(weekStart);
          weekEnd.setDate(weekStart.getDate() + 6);
          weekEnd.setHours(23, 59, 59, 999);
          dateQuery.completedAt = { $gte: weekStart, $lte: weekEnd };
          break;
        case 'month':
          const monthStart = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0);
          const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
          dateQuery.completedAt = { $gte: monthStart, $lte: monthEnd };
          break;
      }
    }

    const query = { ...dateQuery, ...userQuery, ...typeQuery };
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Get completed tasks with user and lead information
    const completedTasks = await CompletedTask.find(query)
      .populate('userId', 'firstName lastName email')
      .populate('leadId', 'name description priority status')
      .sort({ completedAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    // Get time logs for each completed task
    const tasksWithTimeLogs = await Promise.all(
      completedTasks.map(async (task) => {
        const timeLogs = await require('../Models/TimeLog').find({ taskId: task.originalTaskId })
          .sort({ date: -1 });
        return {
          ...task.toObject(),
          timeLogs
        };
      })
    );

    const total = await CompletedTask.countDocuments(query);

    res.json({
      tasks: tasksWithTimeLogs,
      pagination: {
        current: parseInt(page),
        total: Math.ceil(total / parseInt(limit)),
        count: tasksWithTimeLogs.length,
        totalTasks: total
      }
    });
  } catch (err) {
    console.error('Get all completed tasks error:', err);
    res.status(500).json({ error: err.message });
  }
};

// Get completed task statistics for all users (admin only)
exports.getAllCompletedTaskStats = async (req, res) => {
  try {
    const { taskType } = req.query;
    const now = new Date();

    let baseQuery = {};
    if (taskType) {
      baseQuery.taskType = taskType;
    }

    // Today's completed tasks across all users
    const dayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);
    const dayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999);
    const todayCount = await CompletedTask.countDocuments({
      ...baseQuery,
      completedAt: { $gte: dayStart, $lte: dayEnd }
    });

    // This week's completed tasks
    const weekStart = new Date(now);
    weekStart.setDate(now.getDate() - now.getDay());
    weekStart.setHours(0, 0, 0, 0);
    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekStart.getDate() + 6);
    weekEnd.setHours(23, 59, 59, 999);
    const weekCount = await CompletedTask.countDocuments({
      ...baseQuery,
      completedAt: { $gte: weekStart, $lte: weekEnd }
    });

    // This month's completed tasks
    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0);
    const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
    const monthCount = await CompletedTask.countDocuments({
      ...baseQuery,
      completedAt: { $gte: monthStart, $lte: monthEnd }
    });

    // Total completed tasks
    const totalCount = await CompletedTask.countDocuments(baseQuery);

    // Task type breakdown (only if not filtering by task type)
    let taskTypeBreakdown = {};
    if (!taskType) {
      const breakdown = await CompletedTask.aggregate([
        {
          $group: {
            _id: "$taskType",
            count: { $sum: 1 }
          }
        }
      ]);

      breakdown.forEach(item => {
        taskTypeBreakdown[item._id || 'personal'] = item.count;
      });
    }

    // Top performing users (by completed tasks this month)
    const topUsers = await CompletedTask.aggregate([
      {
        $match: {
          ...baseQuery,
          completedAt: { $gte: monthStart, $lte: monthEnd }
        }
      },
      {
        $group: {
          _id: '$userId',
          count: { $sum: 1 }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'user'
        }
      },
      {
        $unwind: '$user'
      },
      {
        $project: {
          _id: 1,
          count: 1,
          'user.firstName': 1,
          'user.lastName': 1,
          'user.email': 1
        }
      },
      {
        $sort: { count: -1 }
      },
      {
        $limit: 5
      }
    ]);

    res.json({
      today: todayCount,
      week: weekCount,
      month: monthCount,
      total: totalCount,
      breakdown: taskTypeBreakdown,
      topUsers
    });
  } catch (err) {
    console.error('Get all completed task stats error:', err);
    res.status(500).json({ error: err.message });
  }
};
