import axios from 'axios';

const API_BASE_URL = 'http://localhost:5001/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add token to requests if available
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle token expiration
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: async (credentials) => {
    const response = await api.post('/auth/login', credentials);
    return response.data;
  },

  signup: async (userData) => {
    const response = await api.post('/auth/signup', userData);
    return response.data;
  },

  updateProfile: async (updateData) => {
    const response = await api.put('/auth/profile', updateData);
    return response.data;
  },
};

// Tasks API
export const tasksAPI = {
  getTasks: async (timeFilter = '', statusFilter = '') => {
    const params = new URLSearchParams();
    if (timeFilter) params.append('filter', timeFilter);
    if (statusFilter) params.append('status', statusFilter);

    const queryString = params.toString();
    const response = await api.get(`/tasks${queryString ? `?${queryString}` : ''}`);
    return response.data;
  },

  getTaskCounts: async () => {
    const response = await api.get('/tasks/counts');
    return response.data;
  },

  createTask: async (taskData) => {
    const response = await api.post('/tasks', taskData);
    return response.data;
  },

  updateTask: async (taskId, taskData) => {
    const response = await api.put(`/tasks/${taskId}`, taskData);
    return response.data;
  },

  deleteTask: async (taskId) => {
    const response = await api.delete(`/tasks/${taskId}`);
    return response.data;
  },

  // Status management
  getStatuses: async () => {
    const response = await api.get('/tasks/statuses');
    return response.data;
  },

  addStatus: async (name, color) => {
    const response = await api.post('/tasks/status', { name, color });
    return response.data;
  },

  deleteStatus: async (statusId) => {
    const response = await api.delete(`/tasks/status/${statusId}`);
    return response.data;
  },

  // Completed tasks management
  getCompletedTasks: async (filter = '', page = 1, limit = 50, taskType = '', leadId = '') => {
    const params = new URLSearchParams();
    if (filter) params.append('filter', filter);
    if (taskType) params.append('taskType', taskType);
    if (leadId) params.append('leadId', leadId);
    params.append('page', page);
    params.append('limit', limit);

    const response = await api.get(`/tasks/completed?${params.toString()}`);
    return response.data;
  },

  getCompletedTaskStats: async (taskType = '', leadId = '') => {
    const params = new URLSearchParams();
    if (taskType) params.append('taskType', taskType);
    if (leadId) params.append('leadId', leadId);

    const queryString = params.toString();
    const response = await api.get(`/tasks/completed/stats${queryString ? `?${queryString}` : ''}`);
    return response.data;
  },

  getLeadCompletedTasks: async (leadId, filter = '', page = 1, limit = 50) => {
    const params = new URLSearchParams();
    if (filter) params.append('filter', filter);
    params.append('page', page);
    params.append('limit', limit);

    const response = await api.get(`/tasks/lead/${leadId}/completed?${params.toString()}`);
    return response.data;
  },

  getLeadTasks: async (leadId, timeFilter = '', statusFilter = '') => {
    const params = new URLSearchParams();
    if (timeFilter) params.append('filter', timeFilter);
    if (statusFilter) params.append('status', statusFilter);

    const queryString = params.toString();
    const response = await api.get(`/tasks/lead/${leadId}${queryString ? `?${queryString}` : ''}`);
    return response.data;
  },

  // Time logging functions
  logTime: async (taskId, timeLogData) => {
    const response = await api.post(`/tasks/${taskId}/log-time`, timeLogData);
    return response.data;
  },

  getTaskTimeLogs: async (taskId, page = 1, limit = 50) => {
    const params = new URLSearchParams();
    params.append('page', page);
    params.append('limit', limit);

    const response = await api.get(`/tasks/${taskId}/time-logs?${params.toString()}`);
    return response.data;
  },

  getDailyTimeLogs: async (date = null) => {
    const params = new URLSearchParams();
    if (date) params.append('date', date);

    const queryString = params.toString();
    const response = await api.get(`/tasks/time-logs/daily${queryString ? `?${queryString}` : ''}`);
    return response.data;
  },

  updateTimeLog: async (timeLogId, updateData) => {
    const response = await api.put(`/tasks/time-logs/${timeLogId}`, updateData);
    return response.data;
  },

  deleteTimeLog: async (timeLogId) => {
    const response = await api.delete(`/tasks/time-logs/${timeLogId}`);
    return response.data;
  },

  // Export functions
  getTasksForExport: async (timeFilter = '') => {
    const params = new URLSearchParams();
    if (timeFilter) params.append('filter', timeFilter);

    const queryString = params.toString();
    const response = await api.get(`/tasks/export${queryString ? `?${queryString}` : ''}`);
    return response.data;
  },

  // Admin functions
  getAllUsersCompletedTasks: async (timeFilter = '', page = 1, limit = 50) => {
    const params = new URLSearchParams();
    if (timeFilter) params.append('filter', timeFilter);
    params.append('page', page);
    params.append('limit', limit);

    const queryString = params.toString();
    const response = await api.get(`/tasks/admin/completed${queryString ? `?${queryString}` : ''}`);
    return response.data;
  },

  getAllUsersTasksForExport: async (timeFilter = '', includeActive = true) => {
    const params = new URLSearchParams();
    if (timeFilter) params.append('filter', timeFilter);
    params.append('includeActive', includeActive);

    const queryString = params.toString();
    const response = await api.get(`/tasks/admin/export${queryString ? `?${queryString}` : ''}`);
    return response.data;
  },

  // Calendar functions
  getCalendarTasks: async (startDate, endDate, includeLeads = true) => {
    const params = new URLSearchParams();
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);
    params.append('includeLeads', includeLeads);

    const queryString = params.toString();
    const response = await api.get(`/tasks/calendar${queryString ? `?${queryString}` : ''}`);
    return response.data;
  },
};

// Notifications API
export const notificationsAPI = {
  getNotifications: async (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    const response = await api.get(`/notifications${queryString ? `?${queryString}` : ''}`);
    return response.data;
  },

  markAsRead: async (notificationId) => {
    const response = await api.put(`/notifications/${notificationId}/read`);
    return response.data;
  },

  markAllAsRead: async () => {
    const response = await api.put('/notifications/read-all');
    return response.data;
  },

  deleteNotification: async (notificationId) => {
    const response = await api.delete(`/notifications/${notificationId}`);
    return response.data;
  },

  cleanupDuplicates: async () => {
    const response = await api.post('/notifications/cleanup-duplicates');
    return response.data;
  },
};

// Admin API
export const adminAPI = {
  getAllUsers: async () => {
    const response = await api.get('/admin/users');
    return response.data;
  },

  getUserDetails: async (userId) => {
    const response = await api.get(`/admin/users/${userId}`);
    return response.data;
  },

  getSystemStats: async () => {
    const response = await api.get('/admin/stats');
    return response.data;
  },

  deleteUser: async (userId) => {
    const response = await api.delete(`/admin/users/${userId}`);
    return response.data;
  },

  // Completed tasks management
  getAllCompletedTasks: async (filter = '', page = 1, limit = 50, userId = '', taskType = '') => {
    const params = new URLSearchParams();
    if (filter) params.append('filter', filter);
    if (page) params.append('page', page);
    if (limit) params.append('limit', limit);
    if (userId) params.append('userId', userId);
    if (taskType) params.append('taskType', taskType);

    const queryString = params.toString();
    const response = await api.get(`/admin/completed-tasks${queryString ? `?${queryString}` : ''}`);
    return response.data;
  },

  getAllCompletedTaskStats: async (taskType = '') => {
    const params = new URLSearchParams();
    if (taskType) params.append('taskType', taskType);

    const queryString = params.toString();
    const response = await api.get(`/admin/completed-tasks/stats${queryString ? `?${queryString}` : ''}`);
    return response.data;
  },
};

// Leads API
export const leadsAPI = {
  // Admin functions
  getAllLeads: async () => {
    const response = await api.get('/leads');
    return response.data;
  },

  createLead: async (leadData) => {
    const response = await api.post('/leads', leadData);
    return response.data;
  },

  updateLead: async (leadId, leadData) => {
    const response = await api.put(`/leads/${leadId}`, leadData);
    return response.data;
  },

  deleteLead: async (leadId) => {
    const response = await api.delete(`/leads/${leadId}`);
    return response.data;
  },

  // User functions
  getMyLeads: async () => {
    const response = await api.get('/leads/my-leads');
    return response.data;
  },

  getLeadDetails: async (leadId) => {
    const response = await api.get(`/leads/${leadId}`);
    return response.data;
  },

  getLeadStats: async () => {
    const response = await api.get('/leads/stats');
    return response.data;
  },

  // Export functions
  getLeadsWithTasksForExport: async (timeFilter = '') => {
    const params = new URLSearchParams();
    if (timeFilter) params.append('filter', timeFilter);

    const queryString = params.toString();
    const response = await api.get(`/leads/export${queryString ? `?${queryString}` : ''}`);
    return response.data;
  },
};

// Convenience functions for components
export const logTimeForTask = (taskId, timeLogData) => {
  return api.post(`/tasks/${taskId}/log-time`, timeLogData);
};

export const getTaskTimeLogsData = (taskId, page = 1, limit = 50) => {
  const params = new URLSearchParams();
  params.append('page', page);
  params.append('limit', limit);
  return api.get(`/tasks/${taskId}/time-logs?${params.toString()}`);
};

export const getDailyTimeLogsData = (date = null) => {
  const params = new URLSearchParams();
  if (date) params.append('date', date);
  const queryString = params.toString();
  return api.get(`/tasks/time-logs/daily${queryString ? `?${queryString}` : ''}`);
};

// Department API
export const departmentAPI = {
  getAll: async () => {
    const response = await api.get('/departments');
    return response.data;
  },
  getById: async (id) => {
    const response = await api.get(`/departments/${id}`);
    return response.data;
  },
  getStats: async () => {
    const response = await api.get('/departments/stats/overview');
    return response.data;
  },
};

// Email Verification API
export const emailVerificationAPI = {
  sendOTP: async (email) => {
    const response = await api.post('/email-verification/send-otp', { email });
    return response.data;
  },
  verifyEmail: async (email, otp) => {
    const response = await api.post('/email-verification/verify', { email, otp });
    return response.data;
  },
  resendOTP: async (email) => {
    const response = await api.post('/email-verification/resend-otp', { email });
    return response.data;
  },
};

// Superadmin API
export const superadminAPI = {
  // Admin management
  createAdmin: async (adminData) => {
    const response = await api.post('/superadmin/admins', adminData);
    return response.data;
  },
  getAllAdmins: async () => {
    const response = await api.get('/superadmin/admins');
    return response.data;
  },
  getAdminById: async (id) => {
    const response = await api.get(`/superadmin/admins/${id}`);
    return response.data;
  },
  updateAdmin: async (id, adminData) => {
    const response = await api.put(`/superadmin/admins/${id}`, adminData);
    return response.data;
  },
  deleteAdmin: async (id) => {
    const response = await api.delete(`/superadmin/admins/${id}`);
    return response.data;
  },

  // Department management
  createDepartment: async (departmentData) => {
    const response = await api.post('/departments', departmentData);
    return response.data;
  },
  updateDepartment: async (id, departmentData) => {
    const response = await api.put(`/departments/${id}`, departmentData);
    return response.data;
  },
  deleteDepartment: async (id) => {
    const response = await api.delete(`/departments/${id}`);
    return response.data;
  },

  // System overview
  getSystemOverview: async () => {
    const response = await api.get('/superadmin/overview');
    return response.data;
  },
};

export default api;
