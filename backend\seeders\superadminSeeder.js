const mongoose = require("mongoose");
const bcrypt = require("bcrypt");
const User = require("../Models/User");
const dotenv = require("dotenv");

dotenv.config();

const seedSuperadmin = async () => {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGO_URI);
    console.log("Connected to MongoDB for superadmin seeding");

    // Check if superadmin already exists
    const existingSuperadmin = await User.findOne({ role: 'superadmin' });
    
    if (existingSuperadmin) {
      console.log("Superadmin account already exists:");
      console.log(`Email: ${existingSuperadmin.email}`);
      console.log("Skipping superadmin creation...");
      return;
    }

    // Create superadmin account
    const superadminData = {
      firstName: "Super",
      lastName: "Administrator", 
      email: "<EMAIL>",
      password: await bcrypt.hash("superadmin123", 10), // Default password
      role: "superadmin",
      gender: "Other",
      address: "System Account",
      isEmailVerified: true, // Superadmin doesn't need email verification
      isActive: true
    };

    const superadmin = await User.create(superadminData);
    
    console.log("✅ Superadmin account created successfully!");
    console.log("📧 Email: <EMAIL>");
    console.log("🔑 Password: superadmin123");
    console.log("⚠️  Please change the default password after first login");
    console.log("🎯 Role: Superadmin");
    console.log("📝 The superadmin can:");
    console.log("   - Create and manage departments");
    console.log("   - Create admin accounts");
    console.log("   - View all system data");
    console.log("   - Manage all users");
    
  } catch (error) {
    console.error("❌ Error seeding superadmin:", error.message);
  } finally {
    await mongoose.disconnect();
    console.log("Disconnected from MongoDB");
  }
};

// Run the seeder
if (require.main === module) {
  seedSuperadmin();
}

module.exports = seedSuperadmin;
