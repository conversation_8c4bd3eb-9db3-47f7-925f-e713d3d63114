import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useToast } from '../../contexts/ToastContext';
import { superadminAPI, departmentAPI } from '../../services/api';
import {
  Users,
  Building,
  Shield,
  BarChart3,
  Plus,
  Edit,
  Trash2,
  Eye,
  UserPlus,
  Settings
} from 'lucide-react';
import DepartmentManagement from './DepartmentManagement';
import AdminManagement from './AdminManagement';
import SystemOverview from './SystemOverview';

const SuperadminDashboard = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalDepartments: 0,
    totalAdmins: 0,
    totalUsers: 0,
    totalUnverifiedUsers: 0
  });

  const { user } = useAuth();
  const { success, error: showError } = useToast();

  useEffect(() => {
    loadSystemStats();
  }, []);

  const loadSystemStats = async () => {
    try {
      setLoading(true);
      const response = await superadminAPI.getSystemOverview();
      setStats(response.overview);
    } catch (error) {
      console.error('Error loading system stats:', error);
      showError('Failed to load system statistics');
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    { id: 'overview', label: 'System Overview', icon: BarChart3 },
    { id: 'departments', label: 'Departments', icon: Building },
    { id: 'admins', label: 'Administrators', icon: Shield },
    { id: 'settings', label: 'Settings', icon: Settings }
  ];

  const StatCard = ({ title, value, icon: Icon, color = 'blue' }) => (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{title}</p>
          <p className={`text-2xl font-bold text-${color}-600 dark:text-${color}-400`}>
            {loading ? '...' : value}
          </p>
        </div>
        <div className={`p-3 rounded-full bg-${color}-100 dark:bg-${color}-900`}>
          <Icon className={`h-6 w-6 text-${color}-600 dark:text-${color}-400`} />
        </div>
      </div>
    </div>
  );

  if (!user || user.role !== 'superadmin') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <Shield className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            Access Denied
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            You don't have permission to access this page.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Superadmin Dashboard
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Welcome back, {user.firstName}! Manage your system from here.
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <div className="bg-purple-100 dark:bg-purple-900 px-3 py-1 rounded-full">
                <span className="text-purple-800 dark:text-purple-200 text-sm font-medium">
                  Superadmin
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatCard
            title="Total Departments"
            value={stats.totalDepartments}
            icon={Building}
            color="blue"
          />
          <StatCard
            title="Total Administrators"
            value={stats.totalAdmins}
            icon={Shield}
            color="green"
          />
          <StatCard
            title="Total Users"
            value={stats.totalUsers}
            icon={Users}
            color="purple"
          />
          <StatCard
            title="Unverified Users"
            value={stats.totalUnverifiedUsers}
            icon={UserPlus}
            color="orange"
          />
        </div>

        {/* Navigation Tabs */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="flex space-x-8 px-6">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                        : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
                    }`}
                  >
                    <Icon className="h-5 w-5" />
                    <span>{tab.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'overview' && (
              <SystemOverview stats={stats} onRefresh={loadSystemStats} />
            )}
            {activeTab === 'departments' && (
              <DepartmentManagement onStatsUpdate={loadSystemStats} />
            )}
            {activeTab === 'admins' && (
              <AdminManagement onStatsUpdate={loadSystemStats} />
            )}
            {activeTab === 'settings' && (
              <div className="text-center py-12">
                <Settings className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  Settings
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  System settings and configuration options coming soon.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SuperadminDashboard;
