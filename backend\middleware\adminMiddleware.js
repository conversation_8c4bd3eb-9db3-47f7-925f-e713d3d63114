const User = require("../Models/User");

const requireAdmin = async (req, res, next) => {
  try {
    // Get user from database to check role
    const user = await User.findById(req.userId);

    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // Allow both admin and superadmin roles
    if (user.role !== 'admin' && user.role !== 'superadmin') {
      return res.status(403).json({
        message: "Access denied. Administrator privileges required."
      });
    }

    // Add user data to request for use in controllers
    req.user = user;
    next();
  } catch (error) {
    return res.status(500).json({ message: "Error verifying admin privileges" });
  }
};

module.exports = requireAdmin;
