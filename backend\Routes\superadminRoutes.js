const express = require("express");
const router = express.Router();
const protect = require("../middleware/authMiddleware");
const requireSuperadmin = require("../middleware/superadminMiddleware");
const {
  createAdmin,
  getAllAdmins,
  getAdminById,
  updateAdmin,
  deleteAdmin,
  getSystemOverview
} = require("../Controllers/superadminController");

// Apply authentication and superadmin middleware to all routes
router.use(protect);
router.use(requireSuperadmin);

// Admin management routes
router.post("/admins", createAdmin);
router.get("/admins", getAllAdmins);
router.get("/admins/:adminId", getAdminById);
router.put("/admins/:adminId", updateAdmin);
router.delete("/admins/:adminId", deleteAdmin);

// System overview
router.get("/overview", getSystemOverview);

module.exports = router;
