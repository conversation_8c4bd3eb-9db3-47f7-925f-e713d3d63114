const express = require("express");
const dotenv = require("dotenv");
const connectDB = require("./Database/db");
const authRoutes = require("./Routes/authRoutes");
const taskRoutes = require("./Routes/taskRoutes");
const notificationRoutes = require("./Routes/notificationRoutes");
const adminRoutes = require("./Routes/adminRoutes");
const leadRoutes = require("./Routes/leadRoutes");
const emailVerificationRoutes = require("./Routes/emailVerificationRoutes");
const departmentRoutes = require("./Routes/departmentRoutes");
const superadminRoutes = require("./Routes/superadminRoutes");
const cors = require("cors");
require("./cron/dailyTaskChecker");

dotenv.config();
connectDB();

const app = express();
app.use(cors());
app.use(express.json());

app.use("/api/auth", authRoutes);
app.use("/api/tasks", taskRoutes);
app.use("/api/notifications", notificationRoutes);
app.use("/api/admin", adminRoutes);
app.use("/api/leads", leadRoutes);
app.use("/api/email-verification", emailVerificationRoutes);
app.use("/api/departments", departmentRoutes);
app.use("/api/superadmin", superadminRoutes);

const PORT = process.env.PORT || 5001;
app.listen(PORT, () => console.log(`Server running on port ${PORT}`));
