{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "dev": "nodemon server.js", "seed:admin": "node seeders/adminSeeder.js", "seed:superadmin": "node seeders/superadminSeeder.js", "cleanup:notifications": "node scripts/cleanupDuplicateNotifications.js", "migrate:task-schema": "node migrations/updateTaskSchema.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.17.0", "mongoose": "^8.15.1", "node-cron": "^4.1.0", "nodemailer": "^7.0.3"}, "devDependencies": {"nodemon": "^3.1.10"}}