const express = require("express");
const router = express.Router();
const protect = require("../middleware/authMiddleware");
const requireSuperadmin = require("../middleware/superadminMiddleware");
const {
  getAllDepartments,
  getDepartmentById,
  createDepartment,
  updateDepartment,
  deleteDepartment,
  getDepartmentStats
} = require("../Controllers/departmentController");

// Apply authentication to all routes
router.use(protect);

// Public routes (for all authenticated users to see departments in dropdowns)
router.get("/", getAllDepartments);
router.get("/:departmentId", getDepartmentById);

// Superadmin only routes
router.use(requireSuperadmin);
router.post("/", createDepartment);
router.put("/:departmentId", updateDepartment);
router.delete("/:departmentId", deleteDepartment);
router.get("/stats/overview", getDepartmentStats);

module.exports = router;
