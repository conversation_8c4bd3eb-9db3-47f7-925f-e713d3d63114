const Department = require("../Models/Department");
const User = require("../Models/User");

// Get all departments
exports.getAllDepartments = async (req, res) => {
  try {
    const departments = await Department.find({ isActive: true })
      .populate('createdBy', 'firstName lastName email')
      .sort({ name: 1 });
    
    res.json({
      success: true,
      count: departments.length,
      departments
    });
  } catch (error) {
    console.error("Error fetching departments:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};

// Get department by ID
exports.getDepartmentById = async (req, res) => {
  try {
    const { departmentId } = req.params;
    
    const department = await Department.findById(departmentId)
      .populate('createdBy', 'firstName lastName email');
    
    if (!department) {
      return res.status(404).json({ message: "Department not found" });
    }

    // Get users in this department
    const users = await User.find({ department: departmentId, isActive: true })
      .select('firstName lastName email role createdAt')
      .sort({ role: 1, firstName: 1 });

    res.json({
      success: true,
      department,
      users,
      userCount: users.length
    });
  } catch (error) {
    console.error("Error fetching department:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};

// Create new department (superadmin only)
exports.createDepartment = async (req, res) => {
  try {
    const { name, description } = req.body;

    // Validation
    if (!name || !description) {
      return res.status(400).json({ 
        message: "Department name and description are required" 
      });
    }

    if (name.length < 2 || name.length > 100) {
      return res.status(400).json({ 
        message: "Department name must be between 2 and 100 characters" 
      });
    }

    if (description.length < 5 || description.length > 500) {
      return res.status(400).json({ 
        message: "Department description must be between 5 and 500 characters" 
      });
    }

    // Check if department with same name already exists
    const existingDepartment = await Department.findOne({ 
      name: { $regex: new RegExp(`^${name}$`, 'i') },
      isActive: true 
    });

    if (existingDepartment) {
      return res.status(400).json({ 
        message: "A department with this name already exists" 
      });
    }

    // Create department
    const department = await Department.create({
      name: name.trim(),
      description: description.trim(),
      createdBy: req.userId
    });

    // Populate createdBy field for response
    await department.populate('createdBy', 'firstName lastName email');

    res.status(201).json({
      success: true,
      message: "Department created successfully",
      department
    });

  } catch (error) {
    console.error("Error creating department:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};

// Update department (superadmin only)
exports.updateDepartment = async (req, res) => {
  try {
    const { departmentId } = req.params;
    const { name, description } = req.body;

    // Find department
    const department = await Department.findById(departmentId);
    if (!department) {
      return res.status(404).json({ message: "Department not found" });
    }

    // Validation
    if (name && (name.length < 2 || name.length > 100)) {
      return res.status(400).json({ 
        message: "Department name must be between 2 and 100 characters" 
      });
    }

    if (description && (description.length < 5 || description.length > 500)) {
      return res.status(400).json({ 
        message: "Department description must be between 5 and 500 characters" 
      });
    }

    // Check if new name conflicts with existing department
    if (name && name.toLowerCase() !== department.name.toLowerCase()) {
      const existingDepartment = await Department.findOne({ 
        name: { $regex: new RegExp(`^${name}$`, 'i') },
        isActive: true,
        _id: { $ne: departmentId }
      });

      if (existingDepartment) {
        return res.status(400).json({ 
          message: "A department with this name already exists" 
        });
      }
    }

    // Update department
    if (name) department.name = name.trim();
    if (description) department.description = description.trim();
    
    await department.save();
    await department.populate('createdBy', 'firstName lastName email');

    res.json({
      success: true,
      message: "Department updated successfully",
      department
    });

  } catch (error) {
    console.error("Error updating department:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};

// Delete department (superadmin only)
exports.deleteDepartment = async (req, res) => {
  try {
    const { departmentId } = req.params;

    // Find department
    const department = await Department.findById(departmentId);
    if (!department) {
      return res.status(404).json({ message: "Department not found" });
    }

    // Check if department has users
    const usersInDepartment = await User.countDocuments({ 
      department: departmentId, 
      isActive: true 
    });

    if (usersInDepartment > 0) {
      return res.status(400).json({ 
        message: `Cannot delete department. It has ${usersInDepartment} active user(s). Please reassign or remove users first.` 
      });
    }

    // Soft delete department
    department.isActive = false;
    await department.save();

    res.json({
      success: true,
      message: "Department deleted successfully"
    });

  } catch (error) {
    console.error("Error deleting department:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};

// Get department statistics
exports.getDepartmentStats = async (req, res) => {
  try {
    const stats = await Department.aggregate([
      { $match: { isActive: true } },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: 'department',
          as: 'users'
        }
      },
      {
        $project: {
          name: 1,
          description: 1,
          createdAt: 1,
          userCount: { $size: '$users' },
          adminCount: {
            $size: {
              $filter: {
                input: '$users',
                cond: { $eq: ['$$this.role', 'admin'] }
              }
            }
          },
          regularUserCount: {
            $size: {
              $filter: {
                input: '$users',
                cond: { $eq: ['$$this.role', 'user'] }
              }
            }
          }
        }
      },
      { $sort: { name: 1 } }
    ]);

    const totalDepartments = await Department.countDocuments({ isActive: true });
    const totalUsers = await User.countDocuments({ isActive: true, role: { $ne: 'superadmin' } });

    res.json({
      success: true,
      totalDepartments,
      totalUsers,
      departments: stats
    });

  } catch (error) {
    console.error("Error fetching department stats:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};
