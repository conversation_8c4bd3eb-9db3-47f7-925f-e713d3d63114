import React from 'react';
import {
  Building,
  Shield,
  Users,
  UserPlus,
  RefreshCw,
  TrendingUp,
  AlertTriangle
} from 'lucide-react';

const SystemOverview = ({ stats, onRefresh }) => {
  const StatCard = ({ title, value, icon: Icon, color = 'blue', description }) => (
    <div className="bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{title}</p>
          <p className={`text-3xl font-bold text-${color}-600 dark:text-${color}-400 mt-1`}>
            {value}
          </p>
          {description && (
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{description}</p>
          )}
        </div>
        <div className={`p-3 rounded-full bg-${color}-100 dark:bg-${color}-900`}>
          <Icon className={`h-8 w-8 text-${color}-600 dark:text-${color}-400`} />
        </div>
      </div>
    </div>
  );

  const DepartmentCard = ({ department }) => (
    <div className="bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 p-4">
      <div className="flex items-center justify-between mb-3">
        <h4 className="font-semibold text-gray-900 dark:text-white">{department.name}</h4>
        <div className="flex items-center space-x-2">
          <div className="bg-blue-100 dark:bg-blue-900 px-2 py-1 rounded-full">
            <span className="text-blue-800 dark:text-blue-200 text-xs font-medium">
              {department.adminCount} Admin{department.adminCount !== 1 ? 's' : ''}
            </span>
          </div>
          <div className="bg-green-100 dark:bg-green-900 px-2 py-1 rounded-full">
            <span className="text-green-800 dark:text-green-200 text-xs font-medium">
              {department.userCount} User{department.userCount !== 1 ? 's' : ''}
            </span>
          </div>
        </div>
      </div>
      
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span className="text-gray-600 dark:text-gray-400">Total Members:</span>
          <span className="font-medium text-gray-900 dark:text-white">
            {department.adminCount + department.userCount}
          </span>
        </div>
        
        {department.adminCount === 0 && (
          <div className="flex items-center space-x-1 text-orange-600 dark:text-orange-400 text-sm">
            <AlertTriangle className="h-4 w-4" />
            <span>No admin assigned</span>
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            System Overview
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Monitor your system's key metrics and performance
          </p>
        </div>
        <button
          onClick={onRefresh}
          className="flex items-center space-x-2 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-500 transition-colors"
        >
          <RefreshCw className="h-5 w-5" />
          <span>Refresh</span>
        </button>
      </div>

      {/* Main Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Departments"
          value={stats.totalDepartments}
          icon={Building}
          color="blue"
          description="Active departments"
        />
        <StatCard
          title="Administrators"
          value={stats.totalAdmins}
          icon={Shield}
          color="green"
          description="Active admin accounts"
        />
        <StatCard
          title="Users"
          value={stats.totalUsers}
          icon={Users}
          color="purple"
          description="Registered users"
        />
        <StatCard
          title="Pending Verification"
          value={stats.totalUnverifiedUsers}
          icon={UserPlus}
          color="orange"
          description="Unverified user accounts"
        />
      </div>

      {/* Department Breakdown */}
      {stats.departmentStats && stats.departmentStats.length > 0 && (
        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Department Breakdown
            </h3>
            <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
              <TrendingUp className="h-4 w-4" />
              <span>{stats.departmentStats.length} departments active</span>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {stats.departmentStats.map((department) => (
              <DepartmentCard key={department._id} department={department} />
            ))}
          </div>
        </div>
      )}

      {/* System Health Indicators */}
      <div className="bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          System Health
        </h3>
        
        <div className="space-y-4">
          {/* Departments without admins */}
          {stats.departmentStats && (
            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-600 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className={`p-2 rounded-full ${
                  stats.departmentStats.filter(d => d.adminCount === 0).length > 0 
                    ? 'bg-orange-100 dark:bg-orange-900' 
                    : 'bg-green-100 dark:bg-green-900'
                }`}>
                  <AlertTriangle className={`h-5 w-5 ${
                    stats.departmentStats.filter(d => d.adminCount === 0).length > 0 
                      ? 'text-orange-600 dark:text-orange-400' 
                      : 'text-green-600 dark:text-green-400'
                  }`} />
                </div>
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">
                    Departments without Admins
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Departments that need admin assignment
                  </p>
                </div>
              </div>
              <div className={`px-3 py-1 rounded-full ${
                stats.departmentStats.filter(d => d.adminCount === 0).length > 0 
                  ? 'bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200' 
                  : 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
              }`}>
                <span className="font-medium">
                  {stats.departmentStats.filter(d => d.adminCount === 0).length}
                </span>
              </div>
            </div>
          )}

          {/* Unverified users */}
          <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-600 rounded-lg">
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-full ${
                stats.totalUnverifiedUsers > 0 
                  ? 'bg-yellow-100 dark:bg-yellow-900' 
                  : 'bg-green-100 dark:bg-green-900'
              }`}>
                <UserPlus className={`h-5 w-5 ${
                  stats.totalUnverifiedUsers > 0 
                    ? 'text-yellow-600 dark:text-yellow-400' 
                    : 'text-green-600 dark:text-green-400'
                }`} />
              </div>
              <div>
                <p className="font-medium text-gray-900 dark:text-white">
                  Pending Email Verifications
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Users waiting to verify their email
                </p>
              </div>
            </div>
            <div className={`px-3 py-1 rounded-full ${
              stats.totalUnverifiedUsers > 0 
                ? 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' 
                : 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
            }`}>
              <span className="font-medium">{stats.totalUnverifiedUsers}</span>
            </div>
          </div>

          {/* System status summary */}
          <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900 rounded-lg">
            <div className="flex items-center space-x-3">
              <div className="p-2 rounded-full bg-green-100 dark:bg-green-800">
                <TrendingUp className="h-5 w-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p className="font-medium text-gray-900 dark:text-white">
                  System Status
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Overall system health
                </p>
              </div>
            </div>
            <div className="px-3 py-1 rounded-full bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-200">
              <span className="font-medium">Operational</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SystemOverview;
