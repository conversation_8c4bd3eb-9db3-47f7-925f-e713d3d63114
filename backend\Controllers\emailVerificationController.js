const User = require("../Models/User");
const { generateOTP, sendVerificationEmail, sendWelcomeEmail } = require("../services/emailService");

// Send OTP for email verification
exports.sendVerificationOTP = async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ message: "Email is required" });
    }

    // Find user by email
    const user = await User.findOne({ email: email.toLowerCase() });
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // Check if user is already verified
    if (user.isEmailVerified) {
      return res.status(400).json({ message: "Email is already verified" });
    }

    // Generate OTP and set expiration (10 minutes)
    const otp = generateOTP();
    const expirationTime = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // Update user with OTP and expiration
    user.emailVerificationToken = otp;
    user.emailVerificationExpires = expirationTime;
    await user.save();

    // Send verification email
    const emailResult = await sendVerificationEmail(email, otp, user.firstName);
    
    if (!emailResult.success) {
      return res.status(500).json({ 
        message: "Failed to send verification email. Please try again." 
      });
    }

    res.json({ 
      message: "Verification OTP sent to your email address. Please check your inbox.",
      expiresIn: "10 minutes"
    });

  } catch (error) {
    console.error("Error sending verification OTP:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};

// Verify OTP and activate account
exports.verifyEmail = async (req, res) => {
  try {
    const { email, otp } = req.body;

    if (!email || !otp) {
      return res.status(400).json({ message: "Email and OTP are required" });
    }

    // Find user by email
    const user = await User.findOne({ email: email.toLowerCase() });
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // Check if user is already verified
    if (user.isEmailVerified) {
      return res.status(400).json({ message: "Email is already verified" });
    }

    // Check if OTP exists and is not expired
    if (!user.emailVerificationToken || !user.emailVerificationExpires) {
      return res.status(400).json({ 
        message: "No verification OTP found. Please request a new one." 
      });
    }

    if (user.emailVerificationExpires < new Date()) {
      return res.status(400).json({ 
        message: "OTP has expired. Please request a new one." 
      });
    }

    // Verify OTP
    if (user.emailVerificationToken !== otp) {
      return res.status(400).json({ message: "Invalid OTP. Please try again." });
    }

    // Mark email as verified and clear verification fields
    user.isEmailVerified = true;
    user.emailVerificationToken = null;
    user.emailVerificationExpires = null;
    await user.save();

    // Send welcome email
    await sendWelcomeEmail(user.email, user.firstName);

    res.json({ 
      message: "Email verified successfully! You can now log in to your account.",
      verified: true
    });

  } catch (error) {
    console.error("Error verifying email:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};

// Resend verification OTP
exports.resendVerificationOTP = async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ message: "Email is required" });
    }

    // Find user by email
    const user = await User.findOne({ email: email.toLowerCase() });
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // Check if user is already verified
    if (user.isEmailVerified) {
      return res.status(400).json({ message: "Email is already verified" });
    }

    // Check rate limiting (prevent spam)
    if (user.emailVerificationExpires && user.emailVerificationExpires > new Date()) {
      const timeLeft = Math.ceil((user.emailVerificationExpires - new Date()) / 1000 / 60);
      return res.status(429).json({ 
        message: `Please wait ${timeLeft} minutes before requesting a new OTP.` 
      });
    }

    // Generate new OTP and set expiration
    const otp = generateOTP();
    const expirationTime = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // Update user with new OTP and expiration
    user.emailVerificationToken = otp;
    user.emailVerificationExpires = expirationTime;
    await user.save();

    // Send verification email
    const emailResult = await sendVerificationEmail(email, otp, user.firstName);
    
    if (!emailResult.success) {
      return res.status(500).json({ 
        message: "Failed to send verification email. Please try again." 
      });
    }

    res.json({ 
      message: "New verification OTP sent to your email address.",
      expiresIn: "10 minutes"
    });

  } catch (error) {
    console.error("Error resending verification OTP:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};
