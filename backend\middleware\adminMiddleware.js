const User = require("../Models/User");
const Admin = require("../Models/Admin");

const requireAdmin = async (req, res, next) => {
  try {
    // Check if user has admin or superadmin role
    if (req.userRole !== 'admin' && req.userRole !== 'superadmin') {
      return res.status(403).json({
        message: "Access denied. Administrator privileges required."
      });
    }

    // User data is already available from authMiddleware
    next();
  } catch (error) {
    return res.status(500).json({ message: "Error verifying admin privileges" });
  }
};

module.exports = requireAdmin;
