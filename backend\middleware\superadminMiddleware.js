const requireSuperadmin = async (req, res, next) => {
  try {
    // Check if user has superadmin role
    if (req.userRole !== 'superadmin') {
      return res.status(403).json({
        message: "Access denied. Superadmin privileges required."
      });
    }

    // User data is already available from authMiddleware
    next();
  } catch (error) {
    return res.status(500).json({ message: "Error verifying superadmin privileges" });
  }
};

module.exports = requireSuperadmin;
