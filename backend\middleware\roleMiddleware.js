const User = require("../Models/User");

// Middleware to check if user has any of the specified roles
const requireRoles = (allowedRoles) => {
  return async (req, res, next) => {
    try {
      // Get user from database to check role
      const user = await User.findById(req.userId);
      
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      if (!allowedRoles.includes(user.role)) {
        return res.status(403).json({ 
          message: `Access denied. Required role(s): ${allowedRoles.join(', ')}` 
        });
      }

      // Add user data to request for use in controllers
      req.user = user;
      next();
    } catch (error) {
      return res.status(500).json({ message: "Error verifying user privileges" });
    }
  };
};

// Specific role middleware functions
const requireSuperadmin = requireRoles(['superadmin']);
const requireAdmin = requireRoles(['admin', 'superadmin']);
const requireUser = requireRoles(['user', 'admin', 'superadmin']);

module.exports = {
  requireRoles,
  requireSuperadmin,
  requireAdmin,
  requireUser
};
