const User = require("../Models/User");
const Department = require("../Models/Department");
const bcrypt = require("bcrypt");

// Create admin account (superadmin only)
exports.createAdmin = async (req, res) => {
  try {
    const { firstName, lastName, email, username, password, confirmPassword, department } = req.body;

    // Validation
    if (!firstName || !lastName || !email || !username || !password || !confirmPassword || !department) {
      return res.status(400).json({ 
        message: "All fields are required: firstName, lastName, email, username, password, confirmPassword, department" 
      });
    }

    // Validate email domain
    const requiredDomain = '@jithpl.com';
    if (!email.endsWith(requiredDomain)) {
      return res.status(400).json({
        message: `Email must be from ${requiredDomain} domain.`
      });
    }

    // Validate password match
    if (password !== confirmPassword) {
      return res.status(400).json({ message: "Passwords do not match" });
    }

    // Validate password strength
    if (password.length < 6) {
      return res.status(400).json({ message: "Password must be at least 6 characters long" });
    }

    // Check if email already exists
    const existingUser = await User.findOne({ email: email.toLowerCase() });
    if (existingUser) {
      return res.status(400).json({ message: "User with this email already exists" });
    }

    // Validate department exists
    const departmentDoc = await Department.findById(department);
    if (!departmentDoc || !departmentDoc.isActive) {
      return res.status(400).json({ message: "Invalid department selected" });
    }

    // Check if department already has an admin
    const existingAdmin = await User.findOne({ 
      department: department, 
      role: 'admin',
      isActive: true 
    });
    
    if (existingAdmin) {
      return res.status(400).json({ 
        message: `Department "${departmentDoc.name}" already has an admin assigned` 
      });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create admin user
    const adminUser = await User.create({
      firstName: firstName.trim(),
      lastName: lastName.trim(),
      email: email.toLowerCase().trim(),
      password: hashedPassword,
      role: 'admin',
      department: department,
      isEmailVerified: true, // Admins don't need email verification
      isActive: true
    });

    // Populate department for response
    await adminUser.populate('department', 'name description');

    // Remove password from response
    const adminResponse = {
      id: adminUser._id,
      firstName: adminUser.firstName,
      lastName: adminUser.lastName,
      fullName: adminUser.fullName,
      email: adminUser.email,
      role: adminUser.role,
      department: adminUser.department,
      isEmailVerified: adminUser.isEmailVerified,
      isActive: adminUser.isActive,
      createdAt: adminUser.createdAt
    };

    res.status(201).json({
      success: true,
      message: "Admin account created successfully",
      admin: adminResponse
    });

  } catch (error) {
    console.error("Error creating admin:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};

// Get all admins (superadmin only)
exports.getAllAdmins = async (req, res) => {
  try {
    const admins = await User.find({ role: 'admin', isActive: true })
      .populate('department', 'name description')
      .select('-password')
      .sort({ firstName: 1 });

    res.json({
      success: true,
      count: admins.length,
      admins
    });

  } catch (error) {
    console.error("Error fetching admins:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};

// Get admin by ID (superadmin only)
exports.getAdminById = async (req, res) => {
  try {
    const { adminId } = req.params;

    const admin = await User.findOne({ _id: adminId, role: 'admin' })
      .populate('department', 'name description')
      .select('-password');

    if (!admin) {
      return res.status(404).json({ message: "Admin not found" });
    }

    // Get users managed by this admin (users in the same department)
    const managedUsers = await User.find({ 
      department: admin.department._id, 
      role: 'user',
      isActive: true 
    })
    .select('firstName lastName email createdAt')
    .sort({ firstName: 1 });

    res.json({
      success: true,
      admin,
      managedUsers,
      managedUserCount: managedUsers.length
    });

  } catch (error) {
    console.error("Error fetching admin:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};

// Update admin (superadmin only)
exports.updateAdmin = async (req, res) => {
  try {
    const { adminId } = req.params;
    const { firstName, lastName, department } = req.body;

    const admin = await User.findOne({ _id: adminId, role: 'admin' });
    if (!admin) {
      return res.status(404).json({ message: "Admin not found" });
    }

    // Validate department if provided
    if (department) {
      const departmentDoc = await Department.findById(department);
      if (!departmentDoc || !departmentDoc.isActive) {
        return res.status(400).json({ message: "Invalid department selected" });
      }

      // Check if new department already has an admin (if changing department)
      if (department !== admin.department.toString()) {
        const existingAdmin = await User.findOne({ 
          department: department, 
          role: 'admin',
          isActive: true,
          _id: { $ne: adminId }
        });
        
        if (existingAdmin) {
          return res.status(400).json({ 
            message: `Department "${departmentDoc.name}" already has an admin assigned` 
          });
        }
      }
    }

    // Update admin
    if (firstName) admin.firstName = firstName.trim();
    if (lastName) admin.lastName = lastName.trim();
    if (department) admin.department = department;

    await admin.save();
    await admin.populate('department', 'name description');

    // Remove password from response
    const adminResponse = admin.toObject();
    delete adminResponse.password;

    res.json({
      success: true,
      message: "Admin updated successfully",
      admin: adminResponse
    });

  } catch (error) {
    console.error("Error updating admin:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};

// Delete admin (superadmin only)
exports.deleteAdmin = async (req, res) => {
  try {
    const { adminId } = req.params;

    const admin = await User.findOne({ _id: adminId, role: 'admin' });
    if (!admin) {
      return res.status(404).json({ message: "Admin not found" });
    }

    // Soft delete admin
    admin.isActive = false;
    await admin.save();

    res.json({
      success: true,
      message: "Admin account deactivated successfully"
    });

  } catch (error) {
    console.error("Error deleting admin:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};

// Get system overview (superadmin only)
exports.getSystemOverview = async (req, res) => {
  try {
    const totalDepartments = await Department.countDocuments({ isActive: true });
    const totalAdmins = await User.countDocuments({ role: 'admin', isActive: true });
    const totalUsers = await User.countDocuments({ role: 'user', isActive: true });
    const totalUnverifiedUsers = await User.countDocuments({ 
      role: 'user', 
      isEmailVerified: false,
      isActive: true 
    });

    // Get department-wise statistics
    const departmentStats = await Department.aggregate([
      { $match: { isActive: true } },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: 'department',
          as: 'users'
        }
      },
      {
        $project: {
          name: 1,
          adminCount: {
            $size: {
              $filter: {
                input: '$users',
                cond: { 
                  $and: [
                    { $eq: ['$$this.role', 'admin'] },
                    { $eq: ['$$this.isActive', true] }
                  ]
                }
              }
            }
          },
          userCount: {
            $size: {
              $filter: {
                input: '$users',
                cond: { 
                  $and: [
                    { $eq: ['$$this.role', 'user'] },
                    { $eq: ['$$this.isActive', true] }
                  ]
                }
              }
            }
          }
        }
      },
      { $sort: { name: 1 } }
    ]);

    res.json({
      success: true,
      overview: {
        totalDepartments,
        totalAdmins,
        totalUsers,
        totalUnverifiedUsers,
        departmentStats
      }
    });

  } catch (error) {
    console.error("Error fetching system overview:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};
