const User = require("../Models/User");
const Admin = require("../Models/Admin");
const Department = require("../Models/Department");
const bcrypt = require("bcrypt");
const jwt = require("jsonwebtoken");
const { generateOTP, sendVerificationEmail } = require("../services/emailService");

exports.signup = async (req, res) => {
  try {
    const { firstName, lastName, email, password, confirmPassword, department, contactNumber } = req.body;

    // Validation
    if (!firstName || !lastName || !email || !password || !confirmPassword || !department) {
      return res.status(400).json({
        message: "All fields are required: firstName, lastName, email, password, confirmPassword, department"
      });
    }

    // Validate password match
    if (password !== confirmPassword) {
      return res.status(400).json({ message: "Passwords do not match" });
    }

    // Validate password strength
    if (password.length < 6) {
      return res.status(400).json({ message: "Password must be at least 6 characters long" });
    }

    // Validate email domain
    const requiredDomain = '@jithpl.com';
    if (!email.endsWith(requiredDomain)) {
      return res.status(400).json({
        message: `Email must be from ${requiredDomain} domain. Please use your company email address.`
      });
    }

    // Prevent superadmin and admin account creation through signup
    const restrictedEmails = ['<EMAIL>', '<EMAIL>'];
    if (restrictedEmails.includes(email.toLowerCase())) {
      return res.status(400).json({
        message: "This email is reserved for system administrators. Please contact your system administrator."
      });
    }

    // Check if user already exists
    const existing = await User.findOne({ email: email.toLowerCase() });
    if (existing) {
      return res.status(400).json({ message: "User already exists with this email address" });
    }

    // Validate department exists
    const departmentDoc = await Department.findById(department);
    if (!departmentDoc || !departmentDoc.isActive) {
      return res.status(400).json({ message: "Invalid department selected" });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Generate OTP for email verification
    const otp = generateOTP();
    const expirationTime = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // Create user (unverified)
    const user = await User.create({
      firstName: firstName.trim(),
      lastName: lastName.trim(),
      email: email.toLowerCase().trim(),
      password: hashedPassword,
      contactNumber: contactNumber?.trim(),
      department: department,
      role: 'user',
      isEmailVerified: false,
      emailVerificationToken: otp,
      emailVerificationExpires: expirationTime
    });

    // Send verification email
    const emailResult = await sendVerificationEmail(user.email, otp, user.firstName);

    if (!emailResult.success) {
      // If email sending fails, delete the user and return error
      await User.findByIdAndDelete(user._id);
      return res.status(500).json({
        message: "Failed to send verification email. Please try again."
      });
    }

    res.status(201).json({
      message: "Account created successfully! Please check your email for verification instructions.",
      email: user.email,
      requiresVerification: true
    });

  } catch (err) {
    console.error("Signup error:", err);
    res.status(500).json({ error: err.message });
  }
};

exports.login = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Try to find user in User collection first
    let user = await User.findOne({ email: email.toLowerCase() })
      .populate('department', 'name description');

    let userType = 'user';
    let userRole = 'user';

    // If not found in User collection, try Admin collection
    if (!user) {
      user = await Admin.findOne({ email: email.toLowerCase() })
        .populate('department', 'name description')
        .populate('createdBy', 'firstName lastName email');

      if (user) {
        userType = 'admin';
        userRole = 'admin';
      }
    } else {
      userRole = user.role; // Could be 'user' or 'superadmin'
    }

    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // Check if account is active
    if (!user.isActive) {
      return res.status(403).json({ message: "Account has been deactivated. Please contact administrator." });
    }

    // Verify password
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(400).json({ message: "Invalid credentials" });
    }

    // Check email verification for regular users
    if (userRole === 'user' && !user.isEmailVerified) {
      return res.status(403).json({
        message: "Please verify your email address before logging in.",
        requiresVerification: true,
        email: user.email
      });
    }

    // Update last login for admins
    if (userType === 'admin') {
      user.lastLogin = new Date();
      await user.save();
    }

    // Generate JWT token
    const token = jwt.sign(
      { id: user._id, role: userRole, userType: userType },
      process.env.JWT_SECRET,
      { expiresIn: "1d" }
    );

    // Return user data without password
    const userData = {
      id: user._id,
      firstName: user.firstName,
      lastName: user.lastName,
      fullName: user.fullName,
      email: user.email,
      username: user.username || null, // Only admins have username
      role: userRole,
      userType: userType, // 'user' or 'admin'
      department: user.department,
      contactNumber: user.contactNumber || null,
      permissions: user.permissions || null, // Only admins have permissions
      gender: user.gender || null,
      dob: user.dob || null,
      address: user.address || null,
      isEmailVerified: user.isEmailVerified,
      lastLogin: user.lastLogin || null,
      createdAt: user.createdAt
    };

    res.json({ token, user: userData });
  } catch (err) {
    console.error("Login error:", err);
    res.status(500).json({ error: err.message });
  }
};

// Update user profile
exports.updateProfile = async (req, res) => {
  try {
    const userId = req.userId;
    const { address, currentPassword, newPassword } = req.body;

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // If updating password, verify current password
    if (newPassword) {
      if (!currentPassword) {
        return res.status(400).json({ message: "Current password is required to change password" });
      }

      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
      if (!isCurrentPasswordValid) {
        return res.status(400).json({ message: "Current password is incorrect" });
      }

      if (newPassword.length < 6) {
        return res.status(400).json({ message: "New password must be at least 6 characters long" });
      }

      // Hash new password
      const salt = await bcrypt.genSalt(10);
      user.password = await bcrypt.hash(newPassword, salt);
    }

    // Update address if provided
    if (address !== undefined) {
      user.address = address;
    }

    await user.save();

    // Return updated user data (without password)
    const updatedUser = {
      id: user._id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      role: user.role,
      gender: user.gender,
      dob: user.dob,
      address: user.address,
    };

    res.json({
      message: "Profile updated successfully",
      user: updatedUser
    });
  } catch (err) {
    console.error('Profile update error:', err);
    res.status(500).json({ message: err.message });
  }
};
